<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SaleRepositoryInterface;
use Illuminate\Support\Facades\Log;

class SaleCreator
{
    private SaleRepositoryInterface $saleRepository;

    public function __construct(SaleRepositoryInterface $saleRepository)
    {
        $this->saleRepository = $saleRepository;
    }
    /**
     * Create a limited sale from ceiling sale data
     *
     * @param mixed $ceilingSale
     * @param mixed $originalSale
     * @param float $limitQuantity
     * @return Sale
     */
    public function createLimitedSale($ceilingSale, $originalSale, float $limitQuantity): Sale
    {
        // Calculate percentage based on the total ceiling sale quantity
        $totalQuantity = $ceilingSale->number_of_units;
        $percentage = $totalQuantity != 0 ? $limitQuantity / $totalQuantity : 0;

        // Use ceiling sale values for proportional calculation
        $proportionalValue = isset($ceilingSale->number_of_values) ? $ceilingSale->number_of_values * $percentage : 0;
        $proportionalBonus = isset($ceilingSale->number_of_bonus) ? $ceilingSale->number_of_bonus * $percentage : 0;

        Log::info('SaleCreator: Creating limited sale', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'original_sale_id' => $originalSale->id ?? 'unknown',
            'total_quantity' => $totalQuantity,
            'limit_quantity' => $limitQuantity,
            'percentage' => $percentage,
            'proportional_value' => $proportionalValue,
            'proportional_bonus' => $proportionalBonus,
            'distributor_id' => $ceilingSale->distributor_id ?? 'unknown'
        ]);

        $sale = $this->saleRepository->create([
            'quantity' => $limitQuantity,
            'value' => $proportionalValue,
            'bonus' => $proportionalBonus,
            'region' => $originalSale->region,
            'distributor_id' => $ceilingSale->distributor_id,
            'product_id' => $ceilingSale->id,
            'date' => $ceilingSale->date,
            'ceiling' => Ceiling::BELOW,
            'sale_ids' => $ceilingSale->sale_ids
        ]);

        Log::debug('SaleCreator: Limited sale created successfully', [
            'new_sale_id' => $sale->id,
            'ceiling_status' => Ceiling::BELOW->value
        ]);

        return $sale;
    }

    /**
     * Create an excess sale from ceiling sale data
     *
     * @param mixed $ceilingSale
     * @param float $excessQuantity
     * @return Sale
     */
    public function createExcessSale($ceilingSale, float $excessQuantity): Sale
    {
        // Calculate percentage based on the total ceiling sale quantity
        $totalQuantity = $ceilingSale->number_of_units;
        $percentage = $totalQuantity != 0 ? $excessQuantity / $totalQuantity : 0;

        // Use ceiling sale values for proportional calculation
        $proportionalValue = isset($ceilingSale->number_of_values) ? $ceilingSale->number_of_values * $percentage : 0;
        $proportionalBonus = isset($ceilingSale->number_of_bonus) ? $ceilingSale->number_of_bonus * $percentage : 0;

        Log::info('SaleCreator: Creating excess sale', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'total_quantity' => $totalQuantity,
            'excess_quantity' => $excessQuantity,
            'percentage' => $percentage,
            'proportional_value' => $proportionalValue,
            'proportional_bonus' => $proportionalBonus,
            'distributor_id' => $ceilingSale->distributor_id ?? 'unknown',
            'region' => 0
        ]);

        $sale = $this->saleRepository->create([
            'quantity' => $excessQuantity,
            'value' => $proportionalValue,
            'bonus' => $proportionalBonus,
            'region' => 0,
            'distributor_id' => $ceilingSale->distributor_id,
            'product_id' => $ceilingSale->id,
            'date' => $ceilingSale->date,
            'ceiling' => Ceiling::DISTRIBUTED,
            'sale_ids' => $ceilingSale->sale_ids
        ]);

        Log::debug('SaleCreator: Excess sale created successfully', [
            'new_sale_id' => $sale->id,
            'ceiling_status' => Ceiling::DISTRIBUTED->value
        ]);

        return $sale;
    }

    /**
     * Update original sales ceiling status to ABOVE
     * This method is used for all strategies where:
     * - We neglect/ignore the original sale in the distribution process
     * - The ABOVE status helps facilitate rollback operations when needed
     * - This maintains audit trail for ceiling violation handling
     * @param mixed $ceilingSale
     * @return bool
     */
    public function updateOriginalSalesCeiling($ceilingSale): bool
    {
        $saleIds = explode(',', $ceilingSale->sale_ids);

        Log::info('SaleCreator: Updating original sales ceiling status to ABOVE', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'sale_ids' => $saleIds,
            'sale_ids_count' => count($saleIds),
            'new_ceiling_status' => Ceiling::ABOVE->value
        ]);

        $affectedRows = $this->saleRepository->updateByIds($saleIds, ['ceiling' => Ceiling::ABOVE]);
        $success = $affectedRows > 0;

        Log::debug('SaleCreator: Original sales ceiling update completed', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'affected_rows' => $affectedRows,
            'success' => $success
        ]);

        return $success;
    }

    /**
     * Attach mapping to sale
     *
     * @param Sale $sale
     * @param int $mappingId
     * @return void
     */
    public function attachMapping(Sale $sale, int $mappingId): void
    {
        $sale->mappings()->attach($mappingId);
    }

    /**
     * Load sale relationships
     *
     * @param Sale $sale
     * @param array $relationships
     * @return Sale
     */
    public function loadRelationships(Sale $sale, array $relationships = ['product:id', 'product.lines:id']): Sale
    {
        return $sale->load($relationships);
    }
}
