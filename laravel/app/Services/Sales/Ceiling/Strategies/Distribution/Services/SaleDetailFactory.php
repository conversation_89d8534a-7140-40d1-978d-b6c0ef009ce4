<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Sale;
use App\SaleDetail;
use Illuminate\Support\Facades\Log;

class SaleDetailFactory
{
    /**
     * Create sale details for a limited sale
     *
     * @param Sale $originalSale
     * @param Sale $limitedSale
     * @param mixed $ceilingSale
     * @return bool
     */
    public function createLimitedSaleDetails(Sale $originalSale, Sale $limitedSale, $ceilingSale): bool
    {
        $details = [];

        foreach ($originalSale->details as $saleDetail) {
            $percentage = $this->calculateDetailPercentage($saleDetail, $originalSale);

            $details[] = [
                'line_id' => $saleDetail->line_id,
                'div_id' => $saleDetail->div_id,
                'brick_id' => $saleDetail->brick_id,
                'quantity' => $limitedSale->quantity * $percentage,
                'bonus' => $limitedSale->bonus * $percentage,
                'value' => $limitedSale->value * $percentage,
                'date' => $ceilingSale->date,
                'sale_id' => $limitedSale->id,
                'file_id' => $saleDetail->file_id,
                'ratio' => $percentage,
                'created_at' => now(),
                'updated_at' => now()
            ];
        }

        return SaleDetail::insert($details);
    }

    /**
     * Create sale details from distribution ratios
     *
     * @param Sale $sale
     * @param array $distributionRatios
     * @param float $quantityMultiplier
     * @param float $bonusMultiplier
     * @param float $valueMultiplier
     * @param float $ratioMultiplier
     * @return array
     */
    public function createDetailsFromRatios(
        Sale $sale,
        array $distributionRatios,
        float $quantityMultiplier = 1.0,
        float $bonusMultiplier = 1.0,
        float $valueMultiplier = 1.0,
        float $ratioMultiplier = 1.0
    ): array {
        Log::debug('SaleDetailFactory: Creating details from ratios', [
            'sale_id' => $sale->id,
            'sale_quantity' => $sale->quantity,
            'sale_value' => $sale->value,
            'sale_bonus' => $sale->bonus,
            'ratios_count' => count($distributionRatios),
            'quantity_multiplier' => $quantityMultiplier,
            'bonus_multiplier' => $bonusMultiplier,
            'value_multiplier' => $valueMultiplier,
            'ratio_multiplier' => $ratioMultiplier
        ]);

        $details = [];
        $totalCalculatedQuantity = 0;
        $totalCalculatedValue = 0;

        foreach ($distributionRatios as $ratio) {
            // Handle both object and array formats for ratio data
            $lineId = is_object($ratio) ? $ratio->line_id : $ratio['line_id'];
            $divId = is_object($ratio) ? $ratio->div_id : $ratio['div_id'];
            $brickId = is_object($ratio) ? $ratio->brick_id : $ratio['brick_id'];
            $percentage = is_object($ratio) ? $ratio->percentage : $ratio['percentage'];

            $calculatedQuantity = $percentage * $sale->quantity * $quantityMultiplier;
            $calculatedValue = $percentage * $sale->value * $valueMultiplier;

            $totalCalculatedQuantity += $calculatedQuantity;
            $totalCalculatedValue += $calculatedValue;

            $details[] = [
                'sale_id' => $sale->id,
                'line_id' => $lineId,
                'div_id' => $divId,
                'brick_id' => $brickId,
                'date' => $sale->date,
                'quantity' => $calculatedQuantity,
                'bonus' => $percentage * $sale->bonus * $bonusMultiplier,
                'value' => $calculatedValue,
                'file_id' => $sale->file_id,
                'ratio' => $percentage * $ratioMultiplier
            ];
        }

        Log::debug('SaleDetailFactory: Details created from ratios', [
            'sale_id' => $sale->id,
            'details_count' => count($details),
            'total_calculated_quantity' => $totalCalculatedQuantity,
            'total_calculated_value' => $totalCalculatedValue,
            'original_sale_quantity' => $sale->quantity,
            'original_sale_value' => $sale->value,
            'quantity_match' => abs($totalCalculatedQuantity - ($sale->quantity * $quantityMultiplier)) < 0.01,
            'value_match' => abs($totalCalculatedValue - ($sale->value * $valueMultiplier)) < 0.01
        ]);

        return $details;
    }

    /**
     * Calculate detail percentage for a sale detail
     *
     * @param mixed $saleDetail
     * @param Sale $originalSale
     * @return float
     */
    private function calculateDetailPercentage($saleDetail, Sale $originalSale): float
    {
        if ($originalSale->quantity == 0) {
            return 0;
        }

        return $saleDetail->quantity / $originalSale->quantity;
    }

    /**
     * Bulk insert sale details
     *
     * @param array $details
     * @return bool
     */
    public function insertDetails(array $details): bool
    {
        if (empty($details)) {
            Log::debug('SaleDetailFactory: No details to insert, returning true');
            return true;
        }

        $totalQuantity = array_sum(array_column($details, 'quantity'));
        $totalValue = array_sum(array_column($details, 'value'));
        $uniqueSaleIds = array_unique(array_column($details, 'sale_id'));

        Log::info('SaleDetailFactory: Inserting sale details', [
            'details_count' => count($details),
            'unique_sale_ids' => $uniqueSaleIds,
            'total_quantity' => $totalQuantity,
            'total_value' => $totalValue
        ]);

        $result = SaleDetail::insert($details);

        Log::debug('SaleDetailFactory: Sale details insertion completed', [
            'success' => $result,
            'details_count' => count($details)
        ]);

        return $result;
    }
}
