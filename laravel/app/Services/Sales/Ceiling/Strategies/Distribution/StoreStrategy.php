<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\LimitCalculatorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SettingsProviderInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\TransactionManagerInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\SalesService;
use Illuminate\Support\Facades\Log;

/**
 * Refactored Store Distribution Strategy
 *
 * This strategy uses 90/10 split distribution for the full original quantity
 * when sales exceed the distribution limit. It skips the limited sale creation
 * and distributes the entire quantity using the split algorithm.
 * It follows SOLID principles and uses dependency injection.
 */
class StoreStrategy extends AbstractDistributionStrategy
{
    private ExcessDistributorInterface $excessDistributor;

    public function __construct(
        TransactionManagerInterface $transactionManager,
        SettingsProviderInterface $settingsProvider,
        LimitCalculatorInterface $limitCalculator,
        SaleDetailFactory $saleDetailFactory,
        SaleCreator $saleCreator,
        SalesService $salesService,
        SplitDistributionAlgorithm $excessDistributor
    ) {
        parent::__construct(
            $transactionManager,
            $settingsProvider,
            $limitCalculator,
            $saleDetailFactory,
            $saleCreator,
            $salesService
        );

        $this->excessDistributor = $excessDistributor;
    }

    /**
     * Override the template method to skip limited sale creation for Store strategy
     *
     * This method processes ceiling sales by creating only one excess sale with the full
     * original quantity and distributing it using the 90/10 split algorithm, skipping
     * the intermediate limited sale creation step.
     *
     * @param mixed $ceilingSale
     * @param array $salesContributionBaseOn
     * @return bool
     */
    public function processCeilingSale($ceilingSale, array $salesContributionBaseOn): bool
    {
        Log::info('StoreStrategy: Processing ceiling sale', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'distributor_id' => $ceilingSale->distributor_id ?? 'unknown',
            'product_id' => $ceilingSale->id ?? 'unknown',
            'quantity' => $ceilingSale->number_of_units ?? 'unknown',
            'date' => $ceilingSale->date ?? 'unknown',
            'sales_contribution_count' => count($salesContributionBaseOn)
        ]);

        // Step 1: Validate ceiling sale
        if (!$this->validateCeilingSale($ceilingSale)) {
            Log::warning('StoreStrategy: Ceiling sale validation failed', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown'
            ]);
            return false;
        }

        Log::debug('StoreStrategy: Ceiling sale validation passed', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown'
        ]);

        // Step 2: Get original sale
        $originalSale = $this->getOriginalSale($ceilingSale);
        if (!$originalSale) {
            Log::error('StoreStrategy: Failed to retrieve original sale', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_ids' => $ceilingSale->sale_ids ?? 'unknown'
            ]);
            return false;
        }

        Log::debug('StoreStrategy: Original sale retrieved successfully', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'original_sale_id' => $originalSale->id,
            'original_sale_quantity' => $originalSale->quantity
        ]);

        // Step 3: Skip limited sale creation - go directly to full quantity distribution
        Log::info('StoreStrategy: Skipping limited sale creation, proceeding with full quantity distribution', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'strategy_type' => 'STORES'
        ]);

        // Step 4: Update original sales ceiling status
        if (!$this->updateOriginalSalesCeiling($ceilingSale)) {
            Log::error('StoreStrategy: Failed to update original sales ceiling status', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_ids' => $ceilingSale->sale_ids ?? 'unknown'
            ]);
            return false;
        }

        Log::debug('StoreStrategy: Original sales ceiling status updated to ABOVE', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown'
        ]);

        // Step 5: Create and distribute full quantity using 90/10 split
        $result = $this->createAndDistributeFullQuantitySale($ceilingSale, $originalSale, $salesContributionBaseOn);

        Log::info('StoreStrategy: Processing completed', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'success' => $result
        ]);

        return $result;
    }

    /**
     * Create and distribute sale with full original quantity using 90/10 split distribution
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @param array $salesContributionBaseOn
     * @return bool
     */
    protected function createAndDistributeFullQuantitySale($ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool
    {
        // Use the full original quantity instead of just the excess
        $fullQuantity = $ceilingSale->number_of_units;

        Log::info('StoreStrategy: Creating full quantity sale for distribution', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'full_quantity' => $fullQuantity,
            'original_sale_id' => $originalSale->id,
            'distributor_id' => $ceilingSale->distributor_id ?? 'unknown',
            'product_id' => $ceilingSale->id ?? 'unknown'
        ]);

        $fullQuantitySale = $this->saleCreator->createExcessSale($ceilingSale, $fullQuantity);
        $fullQuantitySale = $this->saleCreator->loadRelationships($fullQuantitySale);

        Log::debug('StoreStrategy: Full quantity sale created', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'new_sale_id' => $fullQuantitySale->id,
            'new_sale_quantity' => $fullQuantitySale->quantity,
            'new_sale_value' => $fullQuantitySale->value,
            'new_sale_bonus' => $fullQuantitySale->bonus
        ]);

        Log::info('StoreStrategy: Starting 90/10 split distribution', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'sale_to_distribute_id' => $fullQuantitySale->id,
            'distribution_quantity' => $fullQuantitySale->quantity,
            'sales_contribution_count' => count($salesContributionBaseOn)
        ]);

        $distributionSuccess = $this->excessDistributor->distributeExcessSale(
            $fullQuantitySale,
            $salesContributionBaseOn,
            $originalSale
        );

        if ($distributionSuccess) {
            Log::debug('StoreStrategy: Distribution successful, attaching mapping', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_id' => $fullQuantitySale->id,
                'mapping_id' => $ceilingSale->mapping_id ?? 'unknown'
            ]);
            $this->saleCreator->attachMapping($fullQuantitySale, $ceilingSale->mapping_id);
        } else {
            Log::error('StoreStrategy: Distribution failed', [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_id' => $fullQuantitySale->id,
                'quantity' => $fullQuantitySale->quantity
            ]);
        }

        Log::info('StoreStrategy: Full quantity distribution completed', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'success' => $distributionSuccess
        ]);

        return $distributionSuccess;
    }

    /**
     * Create and distribute excess sale using 90/10 split distribution
     *
     * @deprecated This method is kept for backward compatibility but is no longer used
     * in the Store strategy. Use createAndDistributeFullQuantitySale instead.
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @param array $salesContributionBaseOn
     * @return bool
     */
    protected function createAndDistributeExcessSale($ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool
    {
        Log::warning('StoreStrategy: Using deprecated createAndDistributeExcessSale method', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'method' => 'createAndDistributeExcessSale',
            'recommended_method' => 'createAndDistributeFullQuantitySale'
        ]);

        $excessQuantity = $this->excessDistributor->calculateExcessQuantity($ceilingSale);

        Log::debug('StoreStrategy: Calculated excess quantity (deprecated method)', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'excess_quantity' => $excessQuantity,
            'total_quantity' => $ceilingSale->number_of_units ?? 'unknown'
        ]);

        $excessSale = $this->saleCreator->createExcessSale($ceilingSale, $excessQuantity);
        $excessSale = $this->saleCreator->loadRelationships($excessSale);

        $distributionSuccess = $this->excessDistributor->distributeExcessSale(
            $excessSale,
            $salesContributionBaseOn,
            $originalSale,
            $this->getDistributionType()
        );

        if ($distributionSuccess) {
            $this->saleCreator->attachMapping($excessSale, $ceilingSale->mapping_id);
        }

        Log::info('StoreStrategy: Deprecated excess distribution completed', [
            'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
            'success' => $distributionSuccess,
            'excess_quantity' => $excessQuantity
        ]);

        return $distributionSuccess;
    }

    /**
     * Get the DistributionType for this strategy
     *
     * @return DistributionType
     */
    protected function getDistributionType(): DistributionType
    {
        return DistributionType::STORES;
    }
}
